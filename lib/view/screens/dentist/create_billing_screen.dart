import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:platix/controllers/create_billing_controller.dart';
import 'package:platix/data/models/billing_model.dart';
import 'package:platix/data/models/patient_registration_model.dart';
import 'package:platix/utils/app_export.dart';

class CreateBillingScreen extends StatefulWidget {
  final BillingData? billingData;
  final String? billingId;
  final BillingMode mode;

  const CreateBillingScreen({
    super.key,
    this.billingData,
    this.billingId,
    this.mode = BillingMode.create,
  });

  @override
  State<CreateBillingScreen> createState() => _CreateBillingScreenState();
}

class _CreateBillingScreenState extends State<CreateBillingScreen> {
  late CreateBillingController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(CreateBillingController());
    
    // Initialize based on mode
    if (widget.mode == BillingMode.edit && widget.billingData != null) {
      controller.initializeForEdit(widget.billingId!, widget.billingData!);
    } else if (widget.mode == BillingMode.view && widget.billingData != null) {
      controller.initializeForView(widget.billingId!, widget.billingData!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text(controller.modeTitle)),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Obx(() {
        if (controller.isDataLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: controller.formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildPatientSearchSection(),
                const SizedBox(height: 20),
                _buildPatientDetailsSection(),
                const SizedBox(height: 20),
                _buildBillingDetailsSection(),
                const SizedBox(height: 30),
                _buildActionButtons(),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildPatientSearchSection() {
    if (controller.isEditMode || controller.isViewMode) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Search Patient',
          style: CustomTextStyles.b4_1.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Stack(
          children: [
            CustomTextFormField(
              hintText: 'Search by Patient ID, Name, Mobile or Email',
              controller: controller.searchController,
              readOnly: controller.isReadOnly,
              onChanged: (value) => controller.searchPatients(value),
            ),
            Obx(() {
              if (controller.showSearchResults.value && controller.searchResults.isNotEmpty) {
                return Positioned(
                  top: 50,
                  left: 0,
                  right: 0,
                  child: Container(
                    constraints: const BoxConstraints(maxHeight: 200),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: controller.searchResults.length,
                      itemBuilder: (context, index) {
                        final patient = controller.searchResults[index];
                        return ListTile(
                          title: Text(patient.displayName),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('ID: ${patient.patientRegId ?? patient.id}'),
                              Text('Mobile: ${patient.mobile ?? 'N/A'}'),
                            ],
                          ),
                          onTap: () => controller.selectPatient(patient),
                        );
                      },
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      ],
    );
  }

  Widget _buildPatientDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Patient Details',
          style: CustomTextStyles.b4_1.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: LabelTextField(
                label: 'First Name*',
                hint: 'Enter First Name',
                controller: controller.firstNameController,
                isReadOnly: controller.isReadOnly,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: LabelTextField(
                label: 'Last Name*',
                hint: 'Enter Last Name',
                controller: controller.lastNameController,
                isReadOnly: controller.isReadOnly,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: LabelTextField(
                label: 'Patient Registration ID*',
                hint: 'Enter Patient Registration ID',
                controller: controller.patientRegIdController,
                isReadOnly: controller.isReadOnly,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: LabelTextField(
                label: 'Mobile*',
                hint: 'Enter Mobile Number',
                controller: controller.mobileController,
                inputType: TextInputType.phone,
                isReadOnly: controller.isReadOnly,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Gender*',
                    style: CustomTextStyles.b4_1,
                  ),
                  const SizedBox(height: 8),
                  Obx(() => CustomDropdown(
                    hintText: 'Select Gender',
                    items: const ['Male', 'Female'],
                    selectedValue: controller.selectedGender.value.isEmpty
                        ? null
                        : controller.selectedGender.value,
                    onChanged: (value) {
                      if (!controller.isReadOnly) {
                        controller.selectedGender.value = value ?? '';
                      }
                    },
                  )),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: LabelTextField(
                label: 'Age*',
                hint: 'Enter Age',
                controller: controller.ageController,
                inputType: TextInputType.number,
                isReadOnly: controller.isReadOnly,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBillingDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Billing Details',
          style: CustomTextStyles.b4_1.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 16),
        _buildServiceSelection(),
        const SizedBox(height: 16),
        _buildSelectedServices(),
        const SizedBox(height: 16),
        _buildPricingSection(),
        const SizedBox(height: 16),
        _buildPaymentSection(),
      ],
    );
  }

  Widget _buildServiceSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Services*',
          style: CustomTextStyles.b4_1,
        ),
        const SizedBox(height: 8),
        Obx(() {
          if (controller.availableServices.isEmpty) {
            return const Center(child: CircularProgressIndicator());
          }
          
          return Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.medical_services, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Available Services',
                        style: CustomTextStyles.b4_1.copyWith(fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                ),
                Container(
                  constraints: const BoxConstraints(maxHeight: 200),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: controller.availableServices.length,
                    itemBuilder: (context, index) {
                      final service = controller.availableServices[index];
                      final isSelected = controller.selectedServices.contains(service);
                      
                      return ListTile(
                        title: Text(service.displayServiceName),
                        subtitle: Text(service.displayPrice),
                        trailing: controller.isReadOnly 
                            ? null 
                            : Checkbox(
                                value: isSelected,
                                onChanged: (value) {
                                  if (value == true) {
                                    controller.addService(service);
                                  } else {
                                    controller.removeService(service);
                                  }
                                },
                              ),
                        onTap: controller.isReadOnly 
                            ? null 
                            : () {
                                if (isSelected) {
                                  controller.removeService(service);
                                } else {
                                  controller.addService(service);
                                }
                              },
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildSelectedServices() {
    return Obx(() {
      if (controller.selectedServices.isEmpty) {
        return const SizedBox.shrink();
      }
      
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected Services',
            style: CustomTextStyles.b4_1.copyWith(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: controller.selectedServices.map((service) {
                final quantity = controller.serviceQuantities[service.id!] ?? 1;
                final totalPrice = service.price * quantity;
                
                return Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade200),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              service.displayServiceName,
                              style: CustomTextStyles.b4_1.copyWith(fontWeight: FontWeight.w500),
                            ),
                            Text(
                              '₹${service.price.toStringAsFixed(2)} per unit',
                              style: CustomTextStyles.b4_1.copyWith(color: Colors.grey.shade600),
                            ),
                          ],
                        ),
                      ),
                      if (!controller.isReadOnly) ...[
                        Row(
                          children: [
                            IconButton(
                              onPressed: () => controller.updateServiceQuantity(
                                service.id!, 
                                quantity - 1,
                              ),
                              icon: const Icon(Icons.remove_circle_outline),
                              constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                            ),
                            Container(
                              width: 40,
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Text(
                                quantity.toString(),
                                textAlign: TextAlign.center,
                                style: CustomTextStyles.b4_1.copyWith(fontWeight: FontWeight.w500),
                              ),
                            ),
                            IconButton(
                              onPressed: () => controller.updateServiceQuantity(
                                service.id!, 
                                quantity + 1,
                              ),
                              icon: const Icon(Icons.add_circle_outline),
                              constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                            ),
                          ],
                        ),
                      ] else ...[
                        Text(
                          'Qty: $quantity',
                          style: CustomTextStyles.b4_1.copyWith(fontWeight: FontWeight.w500),
                        ),
                      ],
                      const SizedBox(width: 16),
                      Text(
                        '₹${totalPrice.toStringAsFixed(2)}',
                        style: CustomTextStyles.b4_1.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      );
    });
  }

  Widget _buildPricingSection() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: LabelTextField(
                label: 'Price',
                hint: 'Enter Price',
                controller: controller.priceController,
                inputType: TextInputType.number,
                isReadOnly: controller.isReadOnly,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: LabelTextField(
                label: 'Discount (%)',
                hint: 'Enter Discount Percentage',
                controller: controller.discountPercentController,
                inputType: TextInputType.number,
                isReadOnly: controller.isReadOnly,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: LabelTextField(
                label: 'Discount Amount',
                hint: 'Enter Discount Amount',
                controller: controller.discountAmountController,
                inputType: TextInputType.number,
                isReadOnly: controller.isReadOnly,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() => Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.primary.withOpacity(0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Total Amount',
                      style: CustomTextStyles.b4_1.copyWith(fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '₹${controller.totalAmount.value.toStringAsFixed(2)}',
                      style: CustomTextStyles.b4_1.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              )),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPaymentSection() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: LabelTextField(
                label: 'Paid Amount',
                hint: 'Enter Paid Amount',
                controller: controller.paidAmountController,
                inputType: TextInputType.number,
                isReadOnly: controller.isReadOnly,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() => Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: controller.balanceAmount.value > 0 
                      ? Colors.orange.withOpacity(0.1)
                      : Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: controller.balanceAmount.value > 0 
                        ? Colors.orange.withOpacity(0.3)
                        : Colors.green.withOpacity(0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Balance Amount',
                      style: CustomTextStyles.b4_1.copyWith(fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '₹${controller.balanceAmount.value.toStringAsFixed(2)}',
                      style: CustomTextStyles.b4_1.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: controller.balanceAmount.value > 0 
                            ? Colors.orange.shade700
                            : Colors.green.shade700,
                      ),
                    ),
                  ],
                ),
              )),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Source*',
              style: CustomTextStyles.b4_1,
            ),
            const SizedBox(height: 8),
            Obx(() => CustomDropdown(
              hintText: 'Select Payment Source',
              items: const ['Cash', 'Card', 'UPI'],
              selectedValue: controller.selectedPaymentSource.value.isEmpty
                  ? null
                  : controller.selectedPaymentSource.value,
              onChanged: (value) {
                if (!controller.isReadOnly) {
                  controller.selectedPaymentSource.value = value ?? '';
                }
              },
            )),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    if (controller.isViewMode) {
      return CustomElevatedButton(
        onPressed: () {
          controller.currentMode.value = BillingMode.edit;
        },
        text: 'Edit',
        buttonStyle: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
        ),
      );
    }

    return Row(
      children: [
        Expanded(
          child: CustomElevatedButton(
            onPressed: () => Get.back(),
            text: 'Cancel',
            buttonStyle: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey.shade300,
              foregroundColor: Colors.black87,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Obx(() => CustomElevatedButton(
            onPressed: controller.isLoading.value 
                ? null 
                : () => controller.submitBilling(),
            text: controller.submitButtonText,
            buttonStyle: ElevatedButton.styleFrom(
              backgroundColor: controller.isLoading.value 
                  ? Colors.grey 
                  : AppColors.primary,
              foregroundColor: AppColors.white,
            ),
          )),
        ),
      ],
    );
  }
}
