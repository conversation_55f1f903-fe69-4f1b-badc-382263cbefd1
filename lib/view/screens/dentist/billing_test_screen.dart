import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/data/models/billing_model.dart';
import 'package:platix/view/screens/dentist/create_billing_screen.dart';
import 'package:platix/controllers/create_billing_controller.dart';
import 'package:platix/utils/app_export.dart';

class BillingTestScreen extends StatelessWidget {
  const BillingTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Billing Test'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Test Billing Functionality',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            
            // Test Create Billing
            CustomElevatedButton(
              onPressed: () {
                Get.to(() => const CreateBillingScreen(
                  mode: BillingMode.create,
                ));
              },
              text: 'Test Create Billing',
              buttonStyle: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Test Edit Billing (with mock data)
            CustomElevatedButton(
              onPressed: () {
                final mockBillingData = BillingData(
                  id: 'test-id-123',
                  patientRegId: 'REG_TEST123',
                  patientId: 'patient-id-123',
                  firstName: 'John',
                  lastName: 'Doe',
                  mobile: '+919876543210',
                  gender: 'Male',
                  age: 30,
                  services: [
                    BillingService(
                      serviceId: 'service-1',
                      serviceName: 'Dental Cleaning',
                      quantity: 1,
                      price: 500.0,
                    ),
                    BillingService(
                      serviceId: 'service-2',
                      serviceName: 'Tooth Filling',
                      quantity: 2,
                      price: 800.0,
                    ),
                  ],
                  discountPercent: '10',
                  discountAmount: '',
                  totalAmount: '1980.00',
                  paidAmount: '1500.00',
                  balanceAmount: '480.00',
                  paymentSource: 'Card',
                );
                
                Get.to(() => CreateBillingScreen(
                  mode: BillingMode.edit,
                  billingId: 'test-id-123',
                  billingData: mockBillingData,
                ));
              },
              text: 'Test Edit Billing (Mock Data)',
              buttonStyle: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Test View Billing (with mock data)
            CustomElevatedButton(
              onPressed: () {
                final mockBillingData = BillingData(
                  id: 'test-id-456',
                  patientRegId: 'REG_TEST456',
                  patientId: 'patient-id-456',
                  firstName: 'Jane',
                  lastName: 'Smith',
                  mobile: '+919876543211',
                  gender: 'Female',
                  age: 25,
                  services: [
                    BillingService(
                      serviceId: 'service-3',
                      serviceName: 'Root Canal',
                      quantity: 1,
                      price: 2500.0,
                    ),
                  ],
                  discountPercent: '',
                  discountAmount: '200',
                  totalAmount: '2300.00',
                  paidAmount: '2300.00',
                  balanceAmount: '0.00',
                  paymentSource: 'Cash',
                );
                
                Get.to(() => CreateBillingScreen(
                  mode: BillingMode.view,
                  billingId: 'test-id-456',
                  billingData: mockBillingData,
                ));
              },
              text: 'Test View Billing (Mock Data)',
              buttonStyle: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            
            const SizedBox(height: 32),
            
            const Text(
              'Features Tested:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            
            const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('✓ Patient search and selection'),
                Text('✓ Service selection with quantity controls'),
                Text('✓ Price calculations with discounts'),
                Text('✓ Payment tracking (paid/balance amounts)'),
                Text('✓ Form validation'),
                Text('✓ Create/Edit/View modes'),
                Text('✓ Responsive UI design'),
              ],
            ),
          ],
        ),
      ),
    );
  }
}